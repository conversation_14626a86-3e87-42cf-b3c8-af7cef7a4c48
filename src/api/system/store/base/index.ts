import request from '@/config/axios'

// 门店信息 VO
export interface StoreVO {
  id: number // ID
  code: string // 门店编码
  brandId: number // 品牌Id
  brandName: string // 品牌名称
  name: string // 门店名称
  shortName: string // 门店简称
  province: string // 省
  city: string // 市
  district: string // 区/县
  areaId: string // 区域ID
  areaName: string // 区域名称
  operationDeptId: number // 运营部门ID
  operationDeptName: string // 运营部门名称
  operationUserId: number // 运营负责人ID
  operationUserName: string // 运营负责人名称
  mgmtFeeRate: number // 管理费率
  jdId: string // 京东外卖店ID
  jdOpenDate: Date // 京东开业日期
  meituanId: string // 美团外卖店ID
  meituanOpenDate: Date // 美团开业日期
  eleId: string // 饿了么外卖店ID
  eleOpenDate: Date // 饿了么开业日期
}

// 门店信息 API
export const StoreApi = {
  // 查询门店信息分页
  getStorePage: async (params: any) => {
    return await request.get({ url: `/system/store/page`, params })
  },

  // 查询门店信息详情
  getStore: async (id: number) => {
    return await request.get({ url: `/system/store/get?id=` + id })
  },

  // 新增门店信息
  createStore: async (data: StoreVO) => {
    return await request.post({ url: `/system/store/create`, data })
  },

  // 修改门店信息
  updateStore: async (data: StoreVO) => {
    return await request.put({ url: `/system/store/update`, data })
  },

  // 删除门店信息
  deleteStore: async (id: number) => {
    return await request.delete({ url: `/system/store/delete?id=` + id })
  },

  // 导出门店信息 Excel
  exportStore: async (params) => {
    return await request.download({ url: `/system/store/export-excel`, params })
  },
  // 导入数据
  importExcel: async (formData) => {
    return await request.upload({ url: `/system/store/import-excel`, data: formData })
  },
  crawler: async()=>{
   return await request.get({ url: `/system/store/crawler?code=1` ,code: 'a'})
  }




}