import request from '@/config/axios'

export interface CrawlerPostVO {
  qiYeWeiXinCookie: string
  random: string
  deptId: string
  limit: string
}

// 新增部门
export const getQiYwWeiXinDept = (data: CrawlerPostVO) => {
  return request.post({ url: '/system/crawler/weixin-dept', data })
}

//新增用户
export const getQiYwWeiXinUser = (data: CrawlerPostVO) => {
  return request.post({ url: '/system/crawler/weixin-user', data })
}
