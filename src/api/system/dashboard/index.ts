import request from '@/config/axios'


// 仪表板 API
export const DashboardApi = {

  // 刷新仪表板数据
  refreshDashboardData: async () => {
    return await request.post({ url: '/system/dashboard/refresh' })
  },

  // 实时单量数据
  realtimeOrderData: async (params) => {
    return await request.get({ url: '/system/dashboard/get-real-order-count', params })
  },

 operationUesrRealtimeOrderData: async (params) => {
    return await request.get({ url: '/system/dashboard/operation-user/real-order-count', params })
  },

}
