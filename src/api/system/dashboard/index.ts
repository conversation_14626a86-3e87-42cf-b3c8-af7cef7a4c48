import request from '@/config/axios'

// 数据统计相关的接口类型定义
export interface DashboardStatsVO {
  orderCount: number
  dispatchCount: number
  completedCount: number
  totalAmount: number
}

export interface CompareStatsVO {
  dayCompare: string
  dispatchCompare: string
  mergeAmount: number
}

export interface PendingStatsVO {
  dayPending: string
  dispatchPending: string
  pendingAmount: number
}

export interface MonthStatsVO {
  monthCompare: string
  monthDispatch: string
  monthAmount: number
}

export interface TargetDataVO {
  label: string
  value: string
  unit: string
  color: string
}

export interface TableDataVO {
  operationUserName: string
  totalOrders: number
  lastDayOrderCount: string
  lastWeekOrderCount: string
  meituanOrderCount: number
  meituanLastDayOrderCount: string
  meituanLastWeekOrderCount: string
  meituanOpenStoreCount: number
  meituanStoreCount: number
  meituanAvgOrderCount: number
  eleOrderCount: number
  eleLastDayOrderCount: string
  eleLastWeekOrderCount: string
  eleOpenStoreCount: number
  eleStoreCount: number
  eleAvgOrderCount: number
  jdOrderCount: number
  jdLastDayOrderCount: string
  jdLastWeekOrderCount: string
  jdOpenStoreCount: number
  jdStoreCount: number
  jdAvgOrderCount: number
}

// 筛选条件接口
export interface DashboardFilterVO {
  deptId?: number | undefined
  userId?: number | undefined
  brandId?: number | undefined
  dataTime: string
}

// 仪表板 API
export const DashboardApi = {

  // 刷新仪表板数据
  refreshDashboardData: async () => {
    return await request.post({ url: '/system/dashboard/refresh' })
  },

  // 实时单量数据
  realtimeOrderData: async (params: DashboardFilterVO) => {
    return await request.get({ url: '/system/dashboard/get-real-order-count', params })
  },

  // 运营用户实时单量数据
  operationUesrRealtimeOrderData: async (params: DashboardFilterVO) => {
    return await request.get({ url: '/system/dashboard/operation-user/real-order-count', params })
  },

  // 获取品牌数据列表
  getBrandRealtimeOrderData: async (params: DashboardFilterVO) => {
    return await request.get({ url: '/system/dashboard/brand/real-order-count', params })
  },

  // 获取统计数据
  getTodayStats: async (params: DashboardFilterVO) => {
    return await request.get({ url: '/system/dashboard/today-stats', params })
  },

  getCompareStats: async (params: DashboardFilterVO) => {
    return await request.get({ url: '/system/dashboard/compare-stats', params })
  },

  getPendingStats: async (params: DashboardFilterVO) => {
    return await request.get({ url: '/system/dashboard/pending-stats', params })
  },

  getMonthStats: async (params: DashboardFilterVO) => {
    return await request.get({ url: '/system/dashboard/month-stats', params })
  },

  // 获取目标数据
  getOperationTargetData: async (params: DashboardFilterVO) => {
    return await request.get({ url: '/system/dashboard/operation-target', params })
  },

  getBrandTargetData: async (params: DashboardFilterVO) => {
    return await request.get({ url: '/system/dashboard/brand-target', params })
  },

  // 获取表格数据
  getTableDataPage: async (params: DashboardFilterVO & PageParam) => {
    return await request.get({ url: '/system/dashboard/table-data', params })
  }

}
