<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="100px" v-loading="formLoading">
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item label="门店编码" prop="code">
            <el-input v-model="formData.code" placeholder="请输入门店编码" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="门店名称" prop="name">
            <el-input v-model="formData.name" placeholder="请输入门店名称" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="24">
          <!-- <el-form-item label="地区" prop="areaId">
            <el-cascader v-model="formData.areaId" :options="areaList" :props="defaultProps" class="w-1/1" clearable
              filterable placeholder="请选择城市" />
          </el-form-item> -->

          <el-form-item label="地址" prop="areaId">
            <el-cascader v-model="formData.areaId" :options="areaList" :props="defaultProps" class="w-1/1" clearable
              filterable placeholder="请选择城市" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="24">
          <el-form-item label="详细地址" prop="address">
            <el-input v-model="formData.address" placeholder="请输入详细地址" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item label="运营部门" prop="operationDeptId">
            <el-tree-select v-model="formData.operationDeptId" :data="deptTree" :props="defaultProps"
              placeholder="请选择运营部门" @change="handleDeptChange" clearable style="width: 100%;" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="运营负责人" prop="operationUserId">
            <el-select v-model="formData.operationUserId" :disabled="!formData.operationDeptId" filterable
              placeholder="请选择运营负责人" style="width: 100%;">
              <el-option v-for="user in operationUserList" :key="user.id" :label="user.nickname || user.username"
                :value="user.id" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item label="品牌名称" prop="brandId">
            <el-select v-model="formData.brandId" placeholder="请选择品牌名称" filterable clearable style="width: 100%;">
              <el-option v-for="item in brandList" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="管理费率" prop="mgmtFeeRate">
            <el-input v-model="formData.mgmtFeeRate" placeholder="请输入管理费率" />
          </el-form-item>
        </el-col>
      </el-row>
      <!-- 京东店ID和开业日期同一行 -->
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item label="京东店ID" prop="jdId">
            <el-input v-model="formData.jdId" placeholder="请输入京东店ID" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="开业日期" prop="jdOpenDate">
            <el-date-picker v-model="formData.jdOpenDate" type="date" value-format="x" placeholder="选择开业日期"
              style="width: 100%;" />
          </el-form-item>
        </el-col>
      </el-row>
      <!-- 美团店ID和开业日期同一行 -->
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item label="美团店ID" prop="meituanId">
            <el-input v-model="formData.meituanId" placeholder="请输入美团店ID" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="开业日期" prop="meituanOpenDate">
            <el-date-picker v-model="formData.meituanOpenDate" type="date" value-format="x" placeholder="选择开业日期"
              style="width: 100%;" />
          </el-form-item>
        </el-col>
      </el-row>
      <!-- 饿了么店ID和开业日期同一行 -->
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item label="饿了么店ID" prop="eleId">
            <el-input v-model="formData.eleId" placeholder="请输入饿了么店ID" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="开业日期" prop="eleOpenDate">
            <el-date-picker v-model="formData.eleOpenDate" type="date" value-format="x" placeholder="选择开业日期"
              style="width: 100%;" />
          </el-form-item>
        </el-col>
      </el-row>

    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { StoreApi, StoreVO } from '@/api/system/store/base'
import * as AreaApi from '@/api/system/area'
import { defaultProps } from '@/utils/tree'
import { getSimpleDeptList } from '@/api/system/dept'
import { handleTree } from '@/utils/tree'
import { getUserPage } from '@/api/system/user'
import { getSimpleBrandList, type brandVO } from '@/api/system/brand'
// 品牌下拉数据
const brandList = ref<brandVO[]>([]);
const deptList = ref<DeptVO[]>([])
const deptTree = ref<any[]>([])
const operationUserList = ref<UserVO[]>([])

// 运营部门变化时，查询该部门下的用户
const handleDeptChange = async (deptId: number) => {
  formData.value.operationUserId = undefined
  if (!deptId) {
    operationUserList.value = []
    return
  }
  // 查询该部门下的用户，分页大小设为较大值，实际可根据后端接口调整
  const res = await getUserPage({ deptId, pageSize: 100 })
  operationUserList.value = res.list || []
}

// 初始化部门树
onMounted(async () => {
  const flatList = await getSimpleDeptList()
  deptList.value = flatList
  deptTree.value = handleTree(flatList)
})
import type { DeptVO } from '@/api/system/dept'
import type { UserVO } from '@/api/system/user'

/** 门店信息 表单 */
defineOptions({ name: 'StoreForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const areaList = ref([]) // 地区列表
const formData = ref({
  id: undefined,
  code: undefined,
  brandId: undefined,
  brandName: undefined,
  name: undefined,
  shortName: undefined,
  province: undefined,
  city: undefined,
  district: undefined,
  areaId: null,
  address: undefined,
  areaName: undefined,
  operationDeptId: undefined,
  operationDeptName: '',
  operationUserId: undefined,
  operationUserName: '',
  mgmtFeeRate: undefined,
  jdId: undefined,
  jdOpenDate: undefined,
  meituanId: undefined,
  meituanOpenDate: undefined,
  eleId: undefined,
  eleOpenDate: undefined,
  businessUserId: undefined,
  businessUserName: undefined,
  businessDeptId: undefined,
  businessDeptName: undefined,
  businessSignDate: undefined,
  orderSystemCode: undefined,
  orderSystemName: undefined,
  eleBusinessDistrict: undefined,
  meituanBusinessDistrict: undefined,
  jdBusinessDistrict: undefined,
  storeType: undefined,
  supervisionUserId: undefined,
  supervisionUserName: undefined
})
const formRules = reactive({
  code: [{ required: true, message: '门店编码不能为空', trigger: 'blur' }],
  brandName: [{ required: true, message: '品牌名称不能为空', trigger: 'blur' }],
  name: [{ required: true, message: '门店名称不能为空', trigger: 'blur' }],
  areaId: [{ required: true, message: '区域ID不能为空', trigger: 'blur' }],
  areaName: [{ required: true, message: '区域名称不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  // 获得品牌列表
  brandList.value = await getSimpleBrandList();
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await StoreApi.getStore(id)
      if (formData.value.operationUserId) {
        const res = await getUserPage({ deptId: formData.value.operationDeptId, pageSize: 100 })
        operationUserList.value = res.list || []
      }
    } finally {
      formLoading.value = false
    }
  }
  // 获得地区列表
  areaList.value = await AreaApi.getAreaTree()
  // 保持 areaId 为数字类型，和 areaList 节点 id 字段一致，el-cascader 用 defaultProps 即可
  // 获得部门树
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交前补充部门名称、负责人名称、品牌名称
  const dept = deptTree.value.length
    ? findDeptById(deptTree.value, formData.value.operationDeptId)
    : undefined
  formData.value.operationDeptName = dept ? dept.name : ''
  const user = operationUserList.value.find(u => u.id === formData.value.operationUserId)
  formData.value.operationUserName = user ? (user.nickname || user.username) : ''
  // 根据brandId查找brandName
  const brand = brandList.value.find(b => b.id === formData.value.brandId)
  formData.value.brandName = brand ? brand.name : ''
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as StoreVO
    if (formType.value === 'create') {
      await StoreApi.createStore(data)
      message.success(t('common.createSuccess'))
    } else {
      await StoreApi.updateStore(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}
// 递归查找部门树节点
function findDeptById(tree, id) {
  for (const node of tree) {
    if (node.id === id) return node
    if (node.children) {
      const found = findDeptById(node.children, id)
      if (found) return found
    }
  }
  return undefined
}

// 递归查找地区路径（用于el-cascader回显）
// 已移除 getAreaPath 逻辑，采用类型一致性方案

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    code: undefined,
    brandId: undefined,
    brandName: undefined,
    name: undefined,
    shortName: undefined,
    province: undefined,
    city: undefined,
    district: undefined,
    areaId: undefined,
    address: undefined,
    areaName: undefined,
    operationDeptId: undefined,
    operationDeptName: '',
    operationUserId: undefined,
    operationUserName: '',
    mgmtFeeRate: undefined,
    jdId: undefined,
    jdOpenDate: undefined,
    meituanId: undefined,
    meituanOpenDate: undefined,
    eleId: undefined,
    eleOpenDate: undefined,
    businessUserId: undefined,
    businessUserName: undefined,
    businessDeptId: undefined,
    businessDeptName: undefined,
    businessSignDate: undefined,
    orderSystemCode: undefined,
    orderSystemName: undefined,
    eleBusinessDistrict: undefined,
    meituanBusinessDistrict: undefined,
    jdBusinessDistrict: undefined,
    storeType: undefined,
    supervisionUserId: undefined,
    supervisionUserName: undefined
  }
  formRef.value?.resetFields()
  operationUserList.value = []
}
</script>