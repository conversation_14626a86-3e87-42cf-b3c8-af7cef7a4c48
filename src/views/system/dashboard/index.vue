<template>
  <div class="dashboard-container">
    <!-- 顶部统计卡片 -->
    <ContentWrap>
      <div class="mb-4">
        <!-- 店铺数据列表 -->
        <div class="bg-white rounded-lg p-4 shadow-sm border w-full">
          <div class="text-sm text-gray-500 mb-2">{{ dateTime }}</div>
          <el-table :data="shopList" v-loading="loading" border stripe style="width: 100%">
            <el-table-column prop="platform" label="外卖平台" align="center" />
            <el-table-column prop="storeCount" label="店铺数" align="center" />
            <el-table-column prop="storeOpenCount" label="营业数" align="center" />
            <el-table-column prop="orderCount" label="总单量" align="center" />
            <el-table-column prop="avgOrderCount" label="实时均单量" align="center" />
            <el-table-column prop="lastDayOrderCount" label="环比昨日" align="center" />
            <el-table-column prop="lastWeekOrderCount" label="同比上周" align="center" />
          </el-table>
        </div>
      </div>
    </ContentWrap>



    <!-- 实时目标量 -->
    <!-- <ContentWrap> -->
    <!-- <div class="bg-white rounded-lg p-4 shadow-sm border mb-4">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold">实时目标量</h3>
          <div class="flex items-center space-x-2">
            <Icon icon="ep:refresh" class="cursor-pointer text-blue-500" @click="refreshData" />
            <span class="text-sm text-gray-500">{{ lastUpdateTime }}</span>
          </div>
        </div> -->

    <!-- 标签切换 -->
    <!-- <div class="mb-4">
          <el-segmented v-model="activeTab" :options="tabOptions" class="w-full" />
        </div> -->

    <!-- 目标数据展示 -->
    <!-- <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4">
          <div v-for="item in currentTabData" :key="item.label" class="text-center">
            <div class="text-xs text-gray-400 mb-1">{{ item.label }}</div>
            <div class="text-lg font-bold" :class="item.color">{{ item.value }}</div>
            <div class="text-xs text-gray-500">{{ item.unit }}</div>
          </div>
        </div>
      </div> -->
    <!-- </ContentWrap> -->

    <!-- 数据表格 -->
    <ContentWrap>
      <!-- <div class="mb-4"> -->
      <div class="mb-4">
        <div class="p-4 border-b">
          <h3 class="text-lg font-semibold">运营数据</h3>
        </div>

        <el-table v-loading="tableLoading" :data="tableData" stripe style="width: 100%" :show-overflow-tooltip="true">
          <el-table-column prop="operationUserName" label="运营" align="center" />
          <el-table-column label="总单量/占比" width="100" align="center">
            <!-- <template #default="scope">
              <span class="font-semibold">{{ scope.row.totalOrders }}</span>
            </template> -->
            <!-- {{ totalOrderCount }} -->

          </el-table-column>
          <el-table-column prop="lastDayOrderCount" label="环比" align="center" />
          <el-table-column prop="lastWeekOrderCount" label="同比" align="center" />

          <el-table-column prop="meituanOrderCount" label="美单量" align="center" />
          <el-table-column prop="meituanLastDayOrderCount" label="环比" align="center" />
          <el-table-column prop="meituanLastWeekOrderCount" label="同比" align="center" />
          <el-table-column prop="meituanOpenStoreCount" label="美营业数(店)" align="center" />
          <el-table-column prop="meituanStoreCount" label="美店铺数" align="center" />
          <el-table-column prop="meituanAvgOrderCount" label="美店均量" align="center" />


          <el-table-column prop="eleOrderCount" label="饿单量" align="center" />
          <el-table-column prop="eleLastDayOrderCount" label="环比" align="center" />
          <el-table-column prop="eleLastWeekOrderCount" label="同比" align="center" />
          <el-table-column prop="eleOpenStoreCount" label="饿营业数(店)" align="center" />
          <el-table-column prop="eleStoreCount" label="饿店铺数" align="center" />
          <el-table-column prop="eleAvgOrderCount" label="饿店均量" align="center" />

          <el-table-column prop="jdOrderCount" label="京单量" align="center" />
          <el-table-column prop="jdLastDayOrderCount" label="环比" align="center" />
          <el-table-column prop="jdLastWeekOrderCount" label="同比" align="center" />
          <el-table-column prop="jdOpenStoreCount" label="京营业数(店)" align="center" />
          <el-table-column prop="jdStoreCount" label="京店铺数" align="center" />
          <el-table-column prop="jdAvgOrderCount" label="京店均量" align="center" />



          <!-- el-table-column prop="operationUserName" label="运营"    align="center" />
          <el-table-column label="总单量/占比" width="100" align="center">
            <template #default="scope">
              <span class="font-semibold">{{ scope.row.totalOrders }}</span>
            </template> -->

          <!-- </el-table-column>
          <el-table-column prop="lastDayOrderCount" label="环比"    align="center" />
          <el-table-column prop="lastWeekOrderCount" label="同比"    align="center" />

          <el-table-column prop="meituanOrderCount" label="美单量"    align="center" />
          <el-table-column prop="meituanLastDayOrderCount" label="环比"    align="center" />
          <el-table-column prop="meituanLastWeekOrderCount" label="同比"    align="center" />

          <el-table-column prop="eleOrderCount" label="饿单量"    align="center" />
          <el-table-column prop="eleLastDayOrderCount" label="环比"    align="center" />
          <el-table-column prop="eleLastWeekOrderCount" label="同比"    align="center" />

          <el-table-column prop="jdOrderCount" label="京单量"    align="center" />
          <el-table-column prop="jdLastDayOrderCount" label="环比"    align="center" />
          <el-table-column prop="jdLastWeekOrderCount" label="同比"    align="center" />

          <el-table-column prop="avgOrderCount" label="店均量"    align="center" />
          <el-table-column prop="openStoreCount" label="营业数(店)"    align="center" />
          <el-table-column prop="storeCount" label="店铺数"    align="center" /> -->




          <!-- <el-table-column label="总单量" width="100" align="center">
            <template #default="scope">
              <span class="font-semibold">{{ scope.row.operationOrders }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="operationDayCompare" label="并比"    align="center" />
          <el-table-column prop="operationYearCompare" label="同比"    align="center" />

          <el-table-column prop="businessDept" label="招商部门"    align="center" />
          <el-table-column label="总单量" width="100" align="center">
            <template #default="scope">
              <span class="font-semibold">{{ scope.row.businessOrders }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="businessDayCompare" label="并比"    align="center" />
          <el-table-column prop="businessYearCompare" label="同比"    align="center" /> -->
        </el-table>

        <!-- 分页
        <div class="p-4">
          <Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
            @pagination="getTableData" />
        </div> -->
      </div>
    </ContentWrap>
  </div>
</template>

<script setup lang="ts">
import { formatDate } from '@/utils/formatTime'
import { DashboardApi, type DashboardStatsVO, type CompareStatsVO, type PendingStatsVO, type MonthStatsVO, type TargetDataVO, type TableDataVO } from '@/api/system/dashboard'
import { da, tr } from 'element-plus/es/locale'

/** 数据统计仪表板 */
defineOptions({ name: 'SystemDashboard' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const loading = ref(true) // 列表的加载中

// 响应式数据
const tableLoading = ref(false)
const statsLoading = ref(false)
const activeTab = ref('operation')
const lastUpdateTime = ref('')


// 统计数据

// 店铺数据列表
const shopList = ref<any[]>([])
const dateTime = ref('')
const realtimeOrderData = async () => {
  loading.value = true
  try {
    // 获取当前时间字符串，格式：yyyy-MM-dd HH:mm:ss
    const now = new Date();
    const pad = (n: number) => n.toString().padStart(2, '0');
    const dataTime = `${now.getFullYear()}-${pad(now.getMonth() + 1)}-${pad(now.getDate())} ${pad(now.getHours())}:${pad(now.getMinutes())}:${pad(now.getSeconds())}`;
    const res = await DashboardApi.realtimeOrderData({ dataTime });
    console.log("realtimeOrderData", res)
    // 处理响应数据
    if (res && res.targetTime) {
      // targetTime为时间戳，需格式化为yyyy-MM-dd HH:mm
      const t = res.targetTime;
      if (t) {
        console.log("tttttttt", t);
        const d = new Date(t);
        dateTime.value = `${d.getFullYear()}-${pad(d.getMonth() + 1)}-${pad(d.getDate())} ${pad(d.getHours())}:${pad(d.getMinutes())}`;
      } else {
        dateTime.value = '';
      }
      // realOrderCountList转为shopList
      shopList.value = (res.realOrderCountList || []).map(item => ({
        platform: item.takeawayType,
        orderCount: item.totalOrderCount,
        lastDayOrderCount: item.lastDayTotalOrderCount,
        lastWeekOrderCount: item.lastWeekTotalOrderCount,
        avgOrderCount: item.avgOrderCount,
        storeCount: item.storeCount,
        storeOpenCount: item.openStoreCount,


      }));
    }
  } finally {
    loading.value = false
  }
}



const compareStats = ref<CompareStatsVO>({
  dayCompare: '-',
  dispatchCompare: '-',
  mergeAmount: 0
})

const pendingStats = ref<PendingStatsVO>({
  dayPending: '-',
  dispatchPending: '-',
  pendingAmount: 0
})

const monthStats = ref<MonthStatsVO>({
  monthCompare: '-',
  monthDispatch: '-',
  monthAmount: 0
})

// 标签选项
const tabOptions = [
  { label: '运营数据', value: 'operation' },
  { label: '品牌数据', value: 'brand' }
]

// 目标数据
const operationData = ref<TargetDataVO[]>([
  { label: '运营', value: '总单量', unit: '', color: 'text-blue-600' },
  { label: '总单量/并比', value: '并比', unit: '', color: 'text-green-600' },
  { label: '并比', value: '同比', unit: '', color: 'text-orange-600' },
  { label: '同比', value: '美团单量', unit: '', color: 'text-purple-600' },
  { label: '美团单量', value: '并比', unit: '', color: 'text-red-600' },
  { label: '并比', value: '同比', unit: '', color: 'text-indigo-600' },
  { label: '同比', value: '目标单量', unit: '', color: 'text-pink-600' },
  { label: '目标单量', value: '完成率', unit: '', color: 'text-teal-600' }
])

const brandData = ref<TargetDataVO[]>([
  { label: '品牌', value: '总单量', unit: '', color: 'text-blue-600' },
  { label: '总单量', value: '并比', unit: '', color: 'text-green-600' },
  { label: '并比', value: '同比', unit: '', color: 'text-orange-600' },
  { label: '同比', value: '品牌单量', unit: '', color: 'text-purple-600' },
  { label: '品牌单量', value: '并比', unit: '', color: 'text-red-600' },
  { label: '并比', value: '同比', unit: '', color: 'text-indigo-600' },
  { label: '同比', value: '目标完成', unit: '', color: 'text-pink-600' },
  { label: '目标完成', value: '完成率', unit: '', color: 'text-teal-600' }
])

// 当前标签数据
const currentTabData = computed(() => {
  return activeTab.value === 'operation' ? operationData.value : brandData.value
})

// 表格数据
const tableData = ref<TableDataVO[]>([])
const total = ref(0)
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10
})

/** 获取统计数据 */
const getStatsData = async () => {
  statsLoading.value = true
  try {
    // 这里可以调用真实的API
    // const [todayData, compareData, pendingData, monthData] = await Promise.all([
    //   DashboardApi.getTodayStats(),
    //   DashboardApi.getCompareStats(),
    //   DashboardApi.getPendingStats(),
    //   DashboardApi.getMonthStats()
    // ])
    // todayStats.value = todayData
    // compareStats.value = compareData
    // pendingStats.value = pendingData
    // monthStats.value = monthData

    // 模拟数据
    todayStats.value = {
      orderCount: 128,
      dispatchCount: 95,
      completedCount: 87,
      totalAmount: 15680
    }

    compareStats.value = {
      dayCompare: '+12%',
      dispatchCompare: '+8%',
      mergeAmount: 12500
    }

    pendingStats.value = {
      dayPending: '+5%',
      dispatchPending: '+3%',
      pendingAmount: 8900
    }

    monthStats.value = {
      monthCompare: '+15%',
      monthDispatch: '+10%',
      monthAmount: 456780
    }
  } finally {
    statsLoading.value = false
  }
}

/** 获取目标数据 */
const getTargetData = async () => {
  try {
    if (activeTab.value === 'operation') {
      // const data = await DashboardApi.getOperationTargetData()
      // operationData.value = data
      operationData.value = [
        { label: '运营', value: '1,256', unit: '单', color: 'text-blue-600' },
        { label: '总单量/并比', value: '+12%', unit: '', color: 'text-green-600' },
        { label: '并比', value: '+8%', unit: '', color: 'text-orange-600' },
        { label: '同比', value: '+15%', unit: '', color: 'text-purple-600' },
        { label: '美团单量', value: '856', unit: '单', color: 'text-red-600' },
        { label: '并比', value: '+10%', unit: '', color: 'text-indigo-600' },
        { label: '同比', value: '+18%', unit: '', color: 'text-pink-600' },
        { label: '目标单量', value: '1,500', unit: '单', color: 'text-teal-600' }
      ]
    } else {
      // const data = await DashboardApi.getBrandTargetData()
      // brandData.value = data
      brandData.value = [
        { label: '品牌', value: '2,156', unit: '单', color: 'text-blue-600' },
        { label: '总单量', value: '+15%', unit: '', color: 'text-green-600' },
        { label: '并比', value: '+12%', unit: '', color: 'text-orange-600' },
        { label: '同比', value: '+20%', unit: '', color: 'text-purple-600' },
        { label: '品牌单量', value: '1,456', unit: '单', color: 'text-red-600' },
        { label: '并比', value: '+8%', unit: '', color: 'text-indigo-600' },
        { label: '同比', value: '+22%', unit: '', color: 'text-pink-600' },
        { label: '目标完成', value: '85%', unit: '', color: 'text-teal-600' }
      ]
    }
  } catch (error) {
    console.error('获取目标数据失败:', error)
  }
}

/** 获取表格数据 */
const getTableData = async () => {
  tableLoading.value = true
  try {
    // 这里可以调用真实的API
    // 获取当前时间字符串，格式：yyyy-MM-dd HH:mm:ss
    const dataTime = getStartDateTime()

    const data = await DashboardApi.operationUesrRealtimeOrderData({ dataTime })
    // tableData.value = data.list
    // total.value = data.total
    console.log("operationUesrRealtimeOrderData", data)
    tableData.value = data
  } finally {
    tableLoading.value = false
  }
}

/** 刷新数据 */
const refreshData = async () => {
  lastUpdateTime.value = formatDate(new Date())
  await Promise.all([
    getStatsData(),
    getTargetData(),
    getTableData()
  ])
  message.success('数据已刷新')
}

// 监听标签切换
watch(activeTab, () => {
  getTargetData()
})

/** 初始化 */
onMounted(async () => {
  lastUpdateTime.value = formatDate(new Date())
  await Promise.all([
    getStatsData(),
    getTargetData(),
    getTableData(),
    realtimeOrderData()
  ])
})


function getStartDateTime() {
  const now = new Date()
  const pad = (n: number) => n.toString().padStart(2, '0')
  const dataTime = `${now.getFullYear()}-${pad(now.getMonth() + 1)}-${pad(now.getDate())} ${pad(now.getHours())}:${pad(now.getMinutes())}:${pad(now.getSeconds())}`
  return dataTime
}
</script>

<style lang="scss" scoped>
.dashboard-container {
  padding: 16px;
}

.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}

.grid-cols-8 {
  grid-template-columns: repeat(8, minmax(0, 1fr));
}

@media (min-width: 768px) {
  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

@media (min-width: 1024px) {
  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .lg\:grid-cols-8 {
    grid-template-columns: repeat(8, minmax(0, 1fr));
  }
}

.gap-2 {
  gap: 0.5rem;
}

.gap-4 {
  gap: 1rem;
}

.text-center {
  text-align: center;
}

.font-bold {
  font-weight: 700;
}

.font-semibold {
  font-weight: 600;
}

.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-blue-600 {
  color: #2563eb;
}

.text-green-600 {
  color: #16a34a;
}

.text-orange-600 {
  color: #ea580c;
}

.text-purple-600 {
  color: #9333ea;
}

.text-red-600 {
  color: #dc2626;
}

.text-indigo-600 {
  color: #4f46e5;
}

.text-pink-600 {
  color: #db2777;
}

.text-teal-600 {
  color: #0d9488;
}

.text-gray-400 {
  color: #9ca3af;
}

.text-gray-500 {
  color: #6b7280;
}
</style>
