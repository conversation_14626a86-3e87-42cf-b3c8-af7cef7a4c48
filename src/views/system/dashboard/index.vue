<template>
  <div class="dashboard-container">
    <!-- 顶部筛选条件 -->
    <ContentWrap>
      <div class="mb-4">
        <div class="bg-white rounded-lg p-4 shadow-sm border w-full">
          <div class="flex justify-between items-center mb-4">
            <div class="text-sm text-gray-500">{{ dateTime }}</div>
            <div class="flex items-center space-x-4">
              <!-- 运营部门选择 -->
              <div class="flex items-center space-x-2">
                <span class="text-sm text-gray-600">运营部门:</span>
                <el-select
                  v-model="filterParams.deptId"
                  placeholder="全部部门"
                  clearable
                  class="w-32"
                  @change="handleFilterChange"
                >
                  <el-option
                    v-for="dept in deptList"
                    :key="dept.id"
                    :label="dept.name"
                    :value="dept.id"
                  />
                </el-select>
              </div>

              <!-- 人员选择 -->
              <div class="flex items-center space-x-2">
                <span class="text-sm text-gray-600">人员:</span>
                <el-select
                  v-model="filterParams.userId"
                  placeholder="全部人员"
                  clearable
                  class="w-32"
                  @change="handleFilterChange"
                >
                  <el-option
                    v-for="user in userList"
                    :key="user.id"
                    :label="user.nickname"
                    :value="user.id"
                  />
                </el-select>
              </div>

              <!-- 品牌选择 -->
              <div class="flex items-center space-x-2">
                <span class="text-sm text-gray-600">品牌:</span>
                <el-select
                  v-model="filterParams.brandId"
                  placeholder="全部品牌"
                  clearable
                  class="w-32"
                  @change="handleFilterChange"
                >
                  <el-option
                    v-for="brand in brandList"
                    :key="brand.id"
                    :label="brand.name"
                    :value="brand.id"
                  />
                </el-select>
              </div>

              <!-- 刷新按钮 -->
              <el-button type="primary" :icon="RefreshIcon" @click="refreshData" :loading="loading">
                刷新
              </el-button>
            </div>
          </div>

          <!-- 店铺数据列表 -->
          <el-table :data="shopList" v-loading="loading" border stripe style="width: 100%">
            <el-table-column prop="platform" label="外卖平台" align="center" />
            <el-table-column prop="storeCount" label="店铺数" align="center" />
            <el-table-column prop="storeOpenCount" label="营业数" align="center" />
            <el-table-column prop="orderCount" label="总单量" align="center" />
            <el-table-column prop="avgOrderCount" label="实时均单量" align="center" />
            <el-table-column prop="lastDayOrderCount" label="环比昨日" align="center" />
            <el-table-column prop="lastWeekOrderCount" label="同比上周" align="center" />
          </el-table>
        </div>
      </div>
    </ContentWrap>



    <!-- 实时目标量 -->
    <!-- <ContentWrap> -->
    <!-- <div class="bg-white rounded-lg p-4 shadow-sm border mb-4">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold">实时目标量</h3>
          <div class="flex items-center space-x-2">
            <Icon icon="ep:refresh" class="cursor-pointer text-blue-500" @click="refreshData" />
            <span class="text-sm text-gray-500">{{ lastUpdateTime }}</span>
          </div>
        </div> -->

    <!-- 标签切换 -->
    <!-- <div class="mb-4">
          <el-segmented v-model="activeTab" :options="tabOptions" class="w-full" />
        </div> -->

    <!-- 目标数据展示 -->
    <!-- <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4">
          <div v-for="item in currentTabData" :key="item.label" class="text-center">
            <div class="text-xs text-gray-400 mb-1">{{ item.label }}</div>
            <div class="text-lg font-bold" :class="item.color">{{ item.value }}</div>
            <div class="text-xs text-gray-500">{{ item.unit }}</div>
          </div>
        </div>
      </div> -->
    <!-- </ContentWrap> -->

    <!-- 数据表格 -->
    <ContentWrap>
      <div class="mb-4">
        <div class="p-4 border-b flex justify-between items-center">
          <div class="flex items-center space-x-4">
            <h3 class="text-lg font-semibold">数据统计</h3>
            <!-- 标签切换 -->
            <el-segmented v-model="activeTab" :options="tabOptions" />
          </div>
          <div class="text-sm text-gray-500">{{ lastUpdateTime }}</div>
        </div>

        <!-- 运营数据表格 -->
        <el-table v-if="activeTab === 'operation'" v-loading="tableLoading" :data="tableData" stripe style="width: 100%" :show-overflow-tooltip="true">
          <el-table-column prop="operationUserName" label="运营" align="center" />
          <el-table-column prop="totalOrderCount" label="总单量" align="center" />
          <el-table-column prop="lastDayOrderCount" label="环比" align="center" />
          <el-table-column prop="lastWeekOrderCount" label="同比" align="center" />

          <el-table-column prop="meituanOrderCount" label="美单量" align="center" />
          <el-table-column prop="meituanLastDayOrderCount" label="环比" align="center" />
          <el-table-column prop="meituanLastWeekOrderCount" label="同比" align="center" />
          <el-table-column prop="meituanOpenStoreCount" label="美营业数(店)" align="center" />
          <el-table-column prop="meituanStoreCount" label="美店铺数" align="center" />
          <el-table-column prop="meituanAvgOrderCount" label="美店均量" align="center" />

          <el-table-column prop="eleOrderCount" label="饿单量" align="center" />
          <el-table-column prop="eleLastDayOrderCount" label="环比" align="center" />
          <el-table-column prop="eleLastWeekOrderCount" label="同比" align="center" />
          <el-table-column prop="eleOpenStoreCount" label="饿营业数(店)" align="center" />
          <el-table-column prop="eleStoreCount" label="饿店铺数" align="center" />
          <el-table-column prop="eleAvgOrderCount" label="饿店均量" align="center" />

          <el-table-column prop="jdOrderCount" label="京单量" align="center" />
          <el-table-column prop="jdLastDayOrderCount" label="环比" align="center" />
          <el-table-column prop="jdLastWeekOrderCount" label="同比" align="center" />
          <el-table-column prop="jdOpenStoreCount" label="京营业数(店)" align="center" />
          <el-table-column prop="jdStoreCount" label="京店铺数" align="center" />
          <el-table-column prop="jdAvgOrderCount" label="京店均量" align="center" />
        </el-table>

        <!-- 品牌数据表格 -->
        <el-table v-else v-loading="tableLoading" :data="brandTableData" stripe style="width: 100%" :show-overflow-tooltip="true">
          <el-table-column prop="brandName" label="品牌" align="center" />
          <el-table-column prop="totalOrderCount" label="总单量" align="center" />
          <el-table-column prop="lastDayOrderCount" label="环比" align="center" />
          <el-table-column prop="lastWeekOrderCount" label="同比" align="center" />

          <el-table-column prop="meituanOrderCount" label="美单量" align="center" />
          <el-table-column prop="meituanLastDayOrderCount" label="环比" align="center" />
          <el-table-column prop="meituanLastWeekOrderCount" label="同比" align="center" />
          <el-table-column prop="meituanOpenStoreCount" label="美营业数(店)" align="center" />
          <el-table-column prop="meituanStoreCount" label="美店铺数" align="center" />
          <el-table-column prop="meituanAvgOrderCount" label="美店均量" align="center" />

          <el-table-column prop="eleOrderCount" label="饿单量" align="center" />
          <el-table-column prop="eleLastDayOrderCount" label="环比" align="center" />
          <el-table-column prop="eleLastWeekOrderCount" label="同比" align="center" />
          <el-table-column prop="eleOpenStoreCount" label="饿营业数(店)" align="center" />
          <el-table-column prop="eleStoreCount" label="饿店铺数" align="center" />
          <el-table-column prop="eleAvgOrderCount" label="饿店均量" align="center" />

          <el-table-column prop="jdOrderCount" label="京单量" align="center" />
          <el-table-column prop="jdLastDayOrderCount" label="环比" align="center" />
          <el-table-column prop="jdLastWeekOrderCount" label="同比" align="center" />
          <el-table-column prop="jdOpenStoreCount" label="京营业数(店)" align="center" />
          <el-table-column prop="jdStoreCount" label="京店铺数" align="center" />
          <el-table-column prop="jdAvgOrderCount" label="京店均量" align="center" />



          <!-- el-table-column prop="operationUserName" label="运营"    align="center" />
          <el-table-column label="总单量/占比" width="100" align="center">
            <template #default="scope">
              <span class="font-semibold">{{ scope.row.totalOrders }}</span>
            </template> -->

          <!-- </el-table-column>
          <el-table-column prop="lastDayOrderCount" label="环比"    align="center" />
          <el-table-column prop="lastWeekOrderCount" label="同比"    align="center" />

          <el-table-column prop="meituanOrderCount" label="美单量"    align="center" />
          <el-table-column prop="meituanLastDayOrderCount" label="环比"    align="center" />
          <el-table-column prop="meituanLastWeekOrderCount" label="同比"    align="center" />

          <el-table-column prop="eleOrderCount" label="饿单量"    align="center" />
          <el-table-column prop="eleLastDayOrderCount" label="环比"    align="center" />
          <el-table-column prop="eleLastWeekOrderCount" label="同比"    align="center" />

          <el-table-column prop="jdOrderCount" label="京单量"    align="center" />
          <el-table-column prop="jdLastDayOrderCount" label="环比"    align="center" />
          <el-table-column prop="jdLastWeekOrderCount" label="同比"    align="center" />

          <el-table-column prop="avgOrderCount" label="店均量"    align="center" />
          <el-table-column prop="openStoreCount" label="营业数(店)"    align="center" />
          <el-table-column prop="storeCount" label="店铺数"    align="center" /> -->




          <!-- <el-table-column label="总单量" width="100" align="center">
            <template #default="scope">
              <span class="font-semibold">{{ scope.row.operationOrders }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="operationDayCompare" label="并比"    align="center" />
          <el-table-column prop="operationYearCompare" label="同比"    align="center" />

          <el-table-column prop="businessDept" label="招商部门"    align="center" />
          <el-table-column label="总单量" width="100" align="center">
            <template #default="scope">
              <span class="font-semibold">{{ scope.row.businessOrders }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="businessDayCompare" label="并比"    align="center" />
          <el-table-column prop="businessYearCompare" label="同比"    align="center" /> -->
        </el-table>

        <!-- 分页
        <div class="p-4">
          <Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
            @pagination="getTableData" />
        </div> -->
      </div>
    </ContentWrap>
  </div>
</template>

<script setup lang="ts">
import { formatDate } from '@/utils/formatTime'
import { DashboardApi, type DashboardFilterVO, type TableDataVO } from '@/api/system/dashboard'
import * as DeptApi from '@/api/system/dept'
import * as UserApi from '@/api/system/user'
import * as BrandApi from '@/api/system/brand'
import { Refresh as RefreshIcon } from '@element-plus/icons-vue'

/** 数据统计仪表板 */
defineOptions({ name: 'SystemDashboard' })

const message = useMessage() // 消息弹窗
const loading = ref(true) // 列表的加载中

// 响应式数据
const tableLoading = ref(false)
const activeTab = ref('operation')
const lastUpdateTime = ref('')

// 筛选条件
const filterParams = reactive<DashboardFilterVO>({
  deptId: undefined,
  userId: undefined,
  brandId: undefined,
  dataTime: ''
})

// 标签选项
const tabOptions = [
  { label: '运营数据', value: 'operation' },
  { label: '品牌数据', value: 'brand' }
]

// 下拉选择框数据
const deptList = ref<DeptApi.DeptVO[]>([])
const userList = ref<UserApi.UserVO[]>([])
const brandList = ref<BrandApi.brandVO[]>([])

// 店铺数据列表
const shopList = ref<any[]>([])
const dateTime = ref('')
/** 获取筛选选项数据 */
const getFilterOptions = async () => {
  try {
    // 获取部门列表
    deptList.value = await DeptApi.getSimpleDeptList()

    // 获取用户列表
    userList.value = await UserApi.getSimpleUserList()

    // 获取品牌列表
    brandList.value = await BrandApi.getSimpleBrandList()
  } catch (error) {
    console.error('获取筛选选项失败:', error)
  }
}

/** 筛选条件变化处理 */
const handleFilterChange = () => {
  // 当筛选条件变化时，重新获取所有数据
  refreshData()
}

/** 获取当前时间字符串 */
const getCurrentDateTime = () => {
  const now = new Date()
  const pad = (n: number) => n.toString().padStart(2, '0')
  return `${now.getFullYear()}-${pad(now.getMonth() + 1)}-${pad(now.getDate())} ${pad(now.getHours())}:${pad(now.getMinutes())}:${pad(now.getSeconds())}`
}

/** 获取实时订单数据 */
const realtimeOrderData = async () => {
  loading.value = true
  try {
    // 设置当前时间
    filterParams.dataTime = getCurrentDateTime()

    const res = await DashboardApi.realtimeOrderData(filterParams)
    console.log("realtimeOrderData", res)

    // 处理响应数据
    if (res && res.targetTime) {
      // targetTime为时间戳，需格式化为yyyy-MM-dd HH:mm
      const t = res.targetTime
      if (t) {
        const d = new Date(t)
        const pad = (n: number) => n.toString().padStart(2, '0')
        dateTime.value = `${d.getFullYear()}-${pad(d.getMonth() + 1)}-${pad(d.getDate())} ${pad(d.getHours())}:${pad(d.getMinutes())}`
      } else {
        dateTime.value = ''
      }

      // realOrderCountList转为shopList
      shopList.value = (res.realOrderCountList || []).map((item: any) => ({
        platform: item.takeawayType,
        orderCount: item.totalOrderCount,
        lastDayOrderCount: item.lastDayTotalOrderCount,
        lastWeekOrderCount: item.lastWeekTotalOrderCount,
        avgOrderCount: item.avgOrderCount,
        storeCount: item.storeCount,
        storeOpenCount: item.openStoreCount
      }))
    }
  } catch (error) {
    console.error('获取实时订单数据失败:', error)
    message.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 表格数据
const tableData = ref<TableDataVO[]>([])
const brandTableData = ref<any[]>([])

/** 获取运营数据 */
const getOperationData = async () => {
  try {
    // 设置当前时间
    filterParams.dataTime = getCurrentDateTime()

    const data = await DashboardApi.operationUesrRealtimeOrderData(filterParams)
    console.log("operationUesrRealtimeOrderData", data)
    tableData.value = data || []
  } catch (error) {
    console.error('获取运营数据失败:', error)
    message.error('获取运营数据失败')
  }
}

/** 获取品牌数据 */
const getBrandData = async () => {
  try {
    // 设置当前时间
    filterParams.dataTime = getCurrentDateTime()

    const data = await DashboardApi.getBrandRealtimeOrderData(filterParams)
    console.log("getBrandRealtimeOrderData", data)
    brandTableData.value = data || []
  } catch (error) {
    console.error('获取品牌数据失败:', error)
    message.error('获取品牌数据失败')
  }
}

/** 获取表格数据 */
const getTableData = async () => {
  tableLoading.value = true
  try {
    if (activeTab.value === 'operation') {
      await getOperationData()
    } else {
      await getBrandData()
    }
  } finally {
    tableLoading.value = false
  }
}

/** 刷新数据 */
const refreshData = async () => {
  lastUpdateTime.value = formatDate(new Date())
  await Promise.all([
    getFilterOptions(),
    realtimeOrderData(),
    getTableData()
  ])
  message.success('数据已刷新')
}

// 监听标签切换
watch(activeTab, () => {
  getTableData()
})

/** 初始化 */
onMounted(async () => {
  lastUpdateTime.value = formatDate(new Date())
  await Promise.all([
    getFilterOptions(),
    realtimeOrderData(),
    getTableData()
  ])
})
</script>

<style lang="scss" scoped>
.dashboard-container {
  padding: 16px;
}

.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}

.grid-cols-8 {
  grid-template-columns: repeat(8, minmax(0, 1fr));
}

@media (min-width: 768px) {
  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

@media (min-width: 1024px) {
  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .lg\:grid-cols-8 {
    grid-template-columns: repeat(8, minmax(0, 1fr));
  }
}

.gap-2 {
  gap: 0.5rem;
}

.gap-4 {
  gap: 1rem;
}

.text-center {
  text-align: center;
}

.font-bold {
  font-weight: 700;
}

.font-semibold {
  font-weight: 600;
}

.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-blue-600 {
  color: #2563eb;
}

.text-green-600 {
  color: #16a34a;
}

.text-orange-600 {
  color: #ea580c;
}

.text-purple-600 {
  color: #9333ea;
}

.text-red-600 {
  color: #dc2626;
}

.text-indigo-600 {
  color: #4f46e5;
}

.text-pink-600 {
  color: #db2777;
}

.text-teal-600 {
  color: #0d9488;
}

.text-gray-400 {
  color: #9ca3af;
}

.text-gray-500 {
  color: #6b7280;
}
</style>
