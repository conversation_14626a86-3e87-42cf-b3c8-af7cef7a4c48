# 数据统计仪表板页面

## 功能说明

这是一个基于Vue3 + TypeScript + Element Plus开发的数据统计仪表板页面，实现了图片中展示的所有功能模块。

## 主要功能

### 1. 顶部统计卡片
- **今日数据**: 接单数、派单数、完成订单、合计金额
- **对比数据**: 并升日、派升日、合并金额
- **待处理数据**: 并升日、派升日、带升金额  
- **月度数据**: 并升日、派升日、月度金额

### 2. 实时目标量展示
- **标签切换**: 运营数据 / 品牌数据
- **动态数据**: 8个关键指标的实时展示
- **颜色区分**: 不同指标使用不同颜色标识

### 3. 数据表格
- **平台数据**: 外卖平台、总单量、并比、同比
- **运营数据**: 运营部门、总单量、并比、同比
- **招商数据**: 招商部门、总单量、并比、同比
- **分页功能**: 支持数据分页展示

## 技术特点

### 1. 遵循项目编码规范
- 使用Vue3 Composition API
- TypeScript类型安全
- Element Plus组件库
- 响应式布局设计

### 2. 组件化设计
- 统计卡片组件化
- 表格数据结构化
- API接口标准化

### 3. 数据管理
- 统一的API接口定义
- 响应式数据绑定
- 错误处理机制

## 使用方式

### 1. 路由访问
```
/dashboard/system
```

### 2. API接口
```typescript
// 获取统计数据
DashboardApi.getTodayStats()
DashboardApi.getCompareStats()
DashboardApi.getPendingStats()
DashboardApi.getMonthStats()

// 获取目标数据
DashboardApi.getOperationTargetData()
DashboardApi.getBrandTargetData()

// 获取表格数据
DashboardApi.getTableDataPage(params)
```

### 3. 数据刷新
- 手动刷新按钮
- 自动更新时间戳
- 标签切换自动刷新

## 扩展说明

### 1. 真实API集成
当前使用模拟数据，可以通过取消注释API调用代码来集成真实接口：

```typescript
// 取消注释这些代码来使用真实API
// const data = await DashboardApi.getTodayStats()
// todayStats.value = data
```

### 2. 样式定制
页面使用了UnoCSS和自定义SCSS样式，可以根据需要调整：
- 卡片样式
- 颜色主题
- 响应式断点

### 3. 功能扩展
- 添加图表展示
- 增加数据导出
- 实现实时推送
- 添加筛选条件

## 文件结构

```
src/views/system/dashboard/
├── index.vue           # 主页面组件
├── README.md          # 说明文档
src/api/system/dashboard/
└── index.ts           # API接口定义
```

## 依赖说明

- Vue 3.5.12
- TypeScript 5.3.3
- Element Plus 2.9.1
- UnoCSS (样式框架)
- dayjs (时间处理)
