# 数据统计仪表板页面

## 功能说明

这是一个基于Vue3 + TypeScript + Element Plus开发的数据统计仪表板页面，实现了运营数据和品牌数据的实时展示和筛选功能。

## 主要功能

### 1. 顶部筛选条件
- **运营部门选择**: 下拉选择框，支持选择特定部门或全部部门
- **人员选择**: 下拉选择框，支持选择特定人员或全部人员
- **品牌选择**: 下拉选择框，支持选择特定品牌或全部品牌
- **实时刷新**: 手动刷新按钮，更新所有数据

### 2. 店铺数据展示
- **外卖平台数据**: 显示各平台的店铺数、营业数、总单量等
- **实时更新**: 显示数据更新时间
- **环比同比**: 显示环比昨日和同比上周的数据对比

### 3. 数据表格切换
- **运营数据**: 按运营人员维度展示各平台订单数据
- **品牌数据**: 按品牌维度展示各平台订单数据
- **标签切换**: 支持在运营数据和品牌数据之间切换
- **筛选联动**: 所有筛选条件变化时，表格数据自动更新

## 技术特点

### 1. 遵循项目编码规范
- 使用Vue3 Composition API
- TypeScript类型安全
- Element Plus组件库
- 响应式布局设计

### 2. 筛选功能设计
- 三个独立的下拉选择框
- 筛选条件联动更新
- 支持清空选择
- 实时数据刷新

### 3. 数据管理
- 统一的API接口定义
- 响应式数据绑定
- 错误处理机制
- 筛选参数传递

## 使用方式

### 1. 路由访问
```
/dashboard/system
```

### 2. API接口
```typescript
// 获取实时订单数据
DashboardApi.realtimeOrderData(filterParams)

// 获取运营用户数据
DashboardApi.operationUesrRealtimeOrderData(filterParams)

// 获取品牌数据
DashboardApi.getBrandRealtimeOrderData(filterParams)

// 筛选参数格式
interface DashboardFilterVO {
  deptId?: number | undefined
  userId?: number | undefined
  brandId?: number | undefined
  dataTime: string
}
```

### 3. 筛选功能
- 选择运营部门：影响所有数据展示
- 选择人员：进一步细化数据范围
- 选择品牌：按品牌维度筛选
- 手动刷新：重新获取最新数据

## 实现细节

### 1. 筛选条件实现
```typescript
// 筛选条件响应式对象
const filterParams = reactive<DashboardFilterVO>({
  deptId: undefined,
  userId: undefined,
  brandId: undefined,
  dataTime: ''
})

// 筛选条件变化处理
const handleFilterChange = () => {
  refreshData()
}
```

### 2. 数据获取逻辑
```typescript
// 获取筛选选项数据
const getFilterOptions = async () => {
  deptList.value = await DeptApi.getSimpleDeptList()
  userList.value = await UserApi.getSimpleUserList()
  brandList.value = await BrandApi.getSimpleBrandList()
}

// 根据标签获取对应数据
const getTableData = async () => {
  if (activeTab.value === 'operation') {
    await getOperationData()
  } else {
    await getBrandData()
  }
}
```

### 3. 标签切换功能
- 运营数据表格：显示按运营人员维度的数据
- 品牌数据表格：显示按品牌维度的数据
- 自动切换：标签变化时自动获取对应数据

## 文件结构

```
src/views/system/dashboard/
├── index.vue           # 主页面组件
├── README.md          # 说明文档
src/api/system/dashboard/
└── index.ts           # API接口定义
```

## 依赖说明

- Vue 3.5.12
- TypeScript 5.3.3
- Element Plus 2.9.1
- 相关API: DeptApi, UserApi, BrandApi
