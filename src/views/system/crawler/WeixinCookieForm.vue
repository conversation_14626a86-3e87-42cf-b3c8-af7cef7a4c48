<template>
  <Dialog v-model="dialogVisible" title="填写企业微信Cookie">
    <el-form ref="formRef" :model="formData" label-width="100px">
      <el-form-item label="Cookie" prop="cookie" :rules="[{ required: true, message: '<PERSON><PERSON>不能为空', trigger: 'blur' }]">
        <el-input v-model="formData.cookie" type="textarea" :rows="4" placeholder="请输入企业微信Cookie" />
      </el-form-item>
      <el-form-item label="随机数" prop="random" :rules="[{ required: true, message: '随机数不能为空', trigger: 'blur' }]">
        <el-input v-model="formData.random" placeholder="请输入随机数" />
      </el-form-item>
      <el-form-item label="部门ID" prop="deptId">
        <el-input v-model="formData.deptId" placeholder="请输入部门ID" />
      </el-form-item>
      <el-form-item label="限制" prop="limit">
        <el-input v-model="formData.limit" placeholder="请输入限制" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
// 支持传入api方法
const props = defineProps<{ api: (data: any) => Promise<any> }>()
const dialogVisible = ref(false)
const formLoading = ref(false)
const formRef = ref()
const formData = reactive({
  cookie: '',
  random: '',
  deptId: '',
  limit: ''
})

const open = () => {
  dialogVisible.value = true
  formData.cookie = ''
  formData.random = ''
  formData.deptId = ''
  formData.limit = ''
  formRef.value?.resetFields()
}
defineExpose({ open })

const emit = defineEmits(['success'])
const submitForm = async () => {
  await formRef.value.validate()
  formLoading.value = true
  try {
    await props.api({
      qiYeWeiXinCookie: formData.cookie,
      random: formData.random,
      deptId: formData.deptId,
      limit: formData.limit
    })
    dialogVisible.value = false
    emit('success')
  } finally {
    formLoading.value = false
  }
}
</script>
